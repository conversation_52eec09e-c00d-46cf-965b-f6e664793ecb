#include "scheduler.h"

void TaskExeution(void)
{
    static uint32_t debug_counter = 0;

    for (uint8_t i = 0; i < task_num; i++)
    {
        if ((int32_t)(uwTick - (schedul_task[i].task_interval + schedul_task[i].last_run_tick)) >= 0)
        {
            schedul_task[i].last_run_tick = uwTick;

            // 添加调试信息，但只在前几次执行时输出，避免影响性能
            if(debug_counter < 10) {
                Usart0Printf("Executing task %d at tick %d\r\n", i, uwTick);
            }

            schedul_task[i].TaskFunc();
        }
    }

    debug_counter++;
}
