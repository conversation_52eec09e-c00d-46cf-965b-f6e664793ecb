<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\Project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\Project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Sat Aug 02 01:29:16 2025
<BR><P>
<H3>Maximum Stack Usage =       1976 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; TfCardTest &rArr; f_open &rArr; chk_mounted &rArr; disk_initialize &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[24]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[24]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[24]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[71]">BtnEventCallback</a> from btn_app.o(i.BtnEventCallback) referenced from gd32f470vet6_bsp.o(i.BtnPeriphInit)
 <LI><a href="#[72]">BtnGetState</a> from gd32f470vet6_bsp.o(i.BtnGetState) referenced from gd32f470vet6_bsp.o(i.BtnPeriphInit)
 <LI><a href="#[4]">BtnTask</a> from btn_app.o(i.BtnTask) referenced 2 times from gd32f470vet6_bsp.o(.data)
 <LI><a href="#[c]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">EXTI10_15_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6a]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">LedTask</a> from led_app.o(i.LedTask) referenced 2 times from gd32f470vet6_bsp.o(.data)
 <LI><a href="#[b]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">OledTask</a> from oled_app.o(i.OledTask) referenced 2 times from gd32f470vet6_bsp.o(.data)
 <LI><a href="#[10]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">RtcTask</a> from rtc_app.o(i.RtcTask) referenced 2 times from gd32f470vet6_bsp.o(.data)
 <LI><a href="#[43]">SDIO_IRQHandler</a> from gd32f4xx_it.o(i.SDIO_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[e]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6b]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[14]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">TIMER2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[69]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[68]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">USART0_IRQHandler</a> from gd32f4xx_it.o(i.USART0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">Usart0Task</a> from usart_app.o(i.Usart0Task) referenced 2 times from gd32f470vet6_bsp.o(.data)
 <LI><a href="#[12]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[73]">__main</a> from __main.o(!!!main) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[70]">_get_lc_ctype</a> from lc_ctype_c.o(locale$$code) referenced from rt_ctype_table.o(.text)
 <LI><a href="#[6f]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[6d]">_snputc</a> from _snputc.o(.text) referenced from vsnprintf.o(.text)
 <LI><a href="#[6e]">_sputc</a> from _sputc.o(.text) referenced from __2sprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[73]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[74]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[76]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[1b4]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1b5]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1b6]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1b7]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1b8]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[77]"></a>_printf_n</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_n.o(.ARM.Collect$$_printf_percent$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_charcount
</UL>

<P><STRONG><a name="[bf]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[79]"></a>_printf_p</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_p.o(.ARM.Collect$$_printf_percent$$00000002))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_p &rArr; _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
</UL>

<P><STRONG><a name="[7b]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[7d]"></a>_printf_e</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_e.o(.ARM.Collect$$_printf_percent$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_e &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[7e]"></a>_printf_g</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_g.o(.ARM.Collect$$_printf_percent$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_g &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[7f]"></a>_printf_a</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_a.o(.ARM.Collect$$_printf_percent$$00000006))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = _printf_a &rArr; _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[1b9]"></a>_printf_ll</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007))

<P><STRONG><a name="[81]"></a>_printf_i</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_i.o(.ARM.Collect$$_printf_percent$$00000008))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_i &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[83]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[84]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[85]"></a>_printf_o</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_o &rArr; _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[87]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[89]"></a>_printf_lli</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lli &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[8b]"></a>_printf_lld</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lld &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[8c]"></a>_printf_llu</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_llu &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[8d]"></a>_printf_llo</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_llo &rArr; _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
</UL>

<P><STRONG><a name="[8f]"></a>_printf_llx</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_llx &rArr; _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
</UL>

<P><STRONG><a name="[1ba]"></a>_printf_l</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_l.o(.ARM.Collect$$_printf_percent$$00000012))

<P><STRONG><a name="[91]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[93]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[95]"></a>_printf_lc</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[97]"></a>_printf_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_ls &rArr; _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
</UL>

<P><STRONG><a name="[1bb]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[a3]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[99]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[1bc]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[9b]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[1bd]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[1be]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[1bf]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[1c0]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[9d]"></a>__rt_lib_init_lc_ctype_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000012))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_ctype_2 &rArr; _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
</UL>

<P><STRONG><a name="[1c1]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[1c2]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[9e]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[1c3]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[1c4]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[1c5]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[1c6]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[1c7]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[1c8]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[1c9]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[1ca]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[1cb]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[1cc]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[1cd]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[1ce]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[1cf]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[a8]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[1d0]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[1d1]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[1d2]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[1d3]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[1d4]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[1d5]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[1d6]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[1d7]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[75]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[1d8]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[a0]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[a2]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[1d9]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[a4]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 1976 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; TfCardTest &rArr; f_open &rArr; chk_mounted &rArr; disk_initialize &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[1da]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[ce]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[a7]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[1db]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[a9]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[8]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[cd]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[ab]"></a>vsnprintf</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, vsnprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDrawStr
</UL>

<P><STRONG><a name="[ad]"></a>__2sprintf</STRONG> (Thumb, 38 bytes, Stack size 32 bytes, __2sprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[13c]"></a>memcmp</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
</UL>

<P><STRONG><a name="[12d]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[ae]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[1dc]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[af]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[1dd]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1de]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1df]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[12e]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Task
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[b0]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[1e0]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[10a]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetRtc
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadRtc
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[1e1]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1e2]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[b1]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[1e3]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1e4]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1e5]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[b3]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[b4]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[b5]"></a>_printf_truncate_signed</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[b6]"></a>_printf_truncate_unsigned</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[b2]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[82]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_signed
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_i
</UL>

<P><STRONG><a name="[78]"></a>_printf_charcount</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, _printf_charcount.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_n
</UL>

<P><STRONG><a name="[ac]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>

<P><STRONG><a name="[6e]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> __2sprintf.o(.text)
</UL>
<P><STRONG><a name="[6d]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> vsnprintf.o(.text)
</UL>
<P><STRONG><a name="[b9]"></a>_printf_wctomb</STRONG> (Thumb, 182 bytes, Stack size 56 bytes, _printf_wctomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>

<P><STRONG><a name="[8a]"></a>_printf_longlong_dec</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, _printf_longlong_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llu
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lli
</UL>

<P><STRONG><a name="[bc]"></a>_printf_longlong_oct</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[86]"></a>_printf_int_oct</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_o
</UL>

<P><STRONG><a name="[8e]"></a>_printf_ll_oct</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llo
</UL>

<P><STRONG><a name="[bd]"></a>_printf_longlong_hex</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[88]"></a>_printf_int_hex</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[90]"></a>_printf_ll_hex</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llx
</UL>

<P><STRONG><a name="[7a]"></a>_printf_hex_ptr</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_p
</UL>

<P><STRONG><a name="[b8]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[bb]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[b7]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>

<P><STRONG><a name="[1e6]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[c5]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[c8]"></a>_printf_fp_hex_real</STRONG> (Thumb, 756 bytes, Stack size 72 bytes, _printf_fp_hex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[c9]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[92]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[94]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[ca]"></a>_printf_lcs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[96]"></a>_printf_wchar</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lc
</UL>

<P><STRONG><a name="[98]"></a>_printf_wstring</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ls
</UL>

<P><STRONG><a name="[ba]"></a>_wcrtomb</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, _wcrtomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>

<P><STRONG><a name="[a1]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[cb]"></a>__rt_ctype_table</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, rt_ctype_table.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
</UL>

<P><STRONG><a name="[9c]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[c7]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[c1]"></a>_btod_etento</STRONG> (Thumb, 224 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a6]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[1e7]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1e8]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[1b0]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[aa]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[1e9]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1ea]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1eb]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[c2]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d0]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[cf]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[d1]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[d2]"></a>_e2e</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[c3]"></a>_btod_ediv</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[c4]"></a>_btod_emul</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d3]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[10c]"></a>BcdToDec</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f470vet6_bsp.o(i.BcdToDec))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadRtc
</UL>

<P><STRONG><a name="[71]"></a>BtnEventCallback</STRONG> (Thumb, 204 bytes, Stack size 0 bytes, btn_app.o(i.BtnEventCallback))
<BR>[Address Reference Count : 1]<UL><LI> gd32f470vet6_bsp.o(i.BtnPeriphInit)
</UL>
<P><STRONG><a name="[d5]"></a>BtnPeriphInit</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, gd32f470vet6_bsp.o(i.BtnPeriphInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = BtnPeriphInit &rArr; ebtn_init &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_set_config
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_set_combo_suppress_threshold
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_combo_btn_add_btn
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4]"></a>BtnTask</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, btn_app.o(i.BtnTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = BtnTask &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn &rArr; prv_get_combo_btn_by_key_id
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gd32f470vet6_bsp.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>DebugMon_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[119]"></a>DecToBcd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f470vet6_bsp.o(i.DecToBcd))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetRtc
</UL>

<P><STRONG><a name="[dd]"></a>FlashPeriphInit</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, gd32f470vet6_bsp.o(i.FlashPeriphInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = FlashPeriphInit &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[eb]"></a>LedDisp</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, gd32f470vet6_bsp.o(i.LedDisp))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LedDisp
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedTask
</UL>

<P><STRONG><a name="[ed]"></a>LedPeriphInit</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, gd32f470vet6_bsp.o(i.LedPeriphInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LedPeriphInit &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5]"></a>LedTask</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, led_app.o(i.LedTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LedTask &rArr; LedDisp
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedDisp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gd32f470vet6_bsp.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[ee]"></a>OLED_Clear</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = OLED_Clear &rArr; OLED_Write_data &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[f1]"></a>OLED_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = OLED_Init &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
</UL>

<P><STRONG><a name="[f2]"></a>OLED_Set_Position</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, oled.o(i.OLED_Set_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[f3]"></a>OLED_ShowChar</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
</UL>

<P><STRONG><a name="[f4]"></a>OLED_ShowStr</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDrawStr
</UL>

<P><STRONG><a name="[ef]"></a>OLED_Write_cmd</STRONG> (Thumb, 354 bytes, Stack size 16 bytes, oled.o(i.OLED_Write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_config
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop_on_bus
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start_on_bus
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_master_addressing
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_get
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_clear
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_dma_config
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_memory_address_config
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[f0]"></a>OLED_Write_data</STRONG> (Thumb, 354 bytes, Stack size 16 bytes, oled.o(i.OLED_Write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = OLED_Write_data &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_config
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop_on_bus
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start_on_bus
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_master_addressing
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_get
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_clear
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_dma_config
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_memory_address_config
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[107]"></a>OledDrawStr</STRONG> (Thumb, 60 bytes, Stack size 552 bytes, gd32f470vet6_bsp.o(i.OledDrawStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 788 + Unknown Stack Size
<LI>Call Chain = OledDrawStr &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledTask
</UL>

<P><STRONG><a name="[108]"></a>OledPeriphInit</STRONG> (Thumb, 146 bytes, Stack size 8 bytes, gd32f470vet6_bsp.o(i.OledPeriphInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = OledPeriphInit &rArr; OLED_Init &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_mode_addr_config
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_enable
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clock_config
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack_config
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDmaInit
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>OledTask</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, oled_app.o(i.OledTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 796 + Unknown Stack Size
<LI>Call Chain = OledTask &rArr; OledDrawStr &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDrawStr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gd32f470vet6_bsp.o(.data)
</UL>
<P><STRONG><a name="[10]"></a>PendSV_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[109]"></a>ReadRtc</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, gd32f470vet6_bsp.o(i.ReadRtc))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = ReadRtc &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BcdToDec
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcTask
</UL>

<P><STRONG><a name="[10d]"></a>RtcPeriphInit</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, gd32f470vet6_bsp.o(i.RtcPeriphInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 1232 + Unknown Stack Size
<LI>Call Chain = RtcPeriphInit &rArr; SetRtc &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetRtc
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPreConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6]"></a>RtcTask</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, rtc_app.o(i.RtcTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 1208 + Unknown Stack Size
<LI>Call Chain = RtcTask &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadRtc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gd32f470vet6_bsp.o(.data)
</UL>
<P><STRONG><a name="[43]"></a>SDIO_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDIO_IRQHandler &rArr; sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SVC_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[110]"></a>SetRtc</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, gd32f470vet6_bsp.o(i.SetRtc))
<BR><BR>[Stack]<UL><LI>Max Depth = 1224 + Unknown Stack Size
<LI>Call Chain = SetRtc &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DecToBcd
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
</UL>

<P><STRONG><a name="[11]"></a>SysTick_Handler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>SystemInit</STRONG> (Thumb, 364 bytes, Stack size 16 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SystemInit &rArr; system_clock_config &rArr; system_clock_240m_25m_hxtal
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_soft_delay_
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[11e]"></a>TaskExeution</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, scheduler.o(i.TaskExeution))
<BR><BR>[Stack]<UL><LI>Max Depth = 1200 + Unknown Stack Size
<LI>Call Chain = TaskExeution &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11f]"></a>TfCardTest</STRONG> (Thumb, 426 bytes, Stack size 16 bytes, tf_app.o(i.TfCardTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 1976 + Unknown Stack Size
<LI>Call Chain = TfCardTest &rArr; f_open &rArr; chk_mounted &rArr; disk_initialize &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_sd_card_status
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memory_compare
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;card_info_get
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[37]"></a>USART0_IRQHandler</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART0_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_disable
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_get
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_config
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[131]"></a>Usart0PeriphInit</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, gd32f470vet6_bsp.o(i.Usart0PeriphInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = Usart0PeriphInit &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_dma_receive_config
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0DmaInit
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[112]"></a>Usart0Printf</STRONG> (Thumb, 102 bytes, Stack size 1064 bytes, gd32f470vet6_bsp.o(i.Usart0Printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 1192 + Unknown Stack Size
<LI>Call Chain = Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_sd_card_status
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;card_info_get
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TaskExeution
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Task
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcTask
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetRtc
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
</UL>

<P><STRONG><a name="[3]"></a>Usart0Task</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, usart_app.o(i.Usart0Task))
<BR><BR>[Stack]<UL><LI>Max Depth = 1200 + Unknown Stack Size
<LI>Call Chain = Usart0Task &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gd32f470vet6_bsp.o(.data)
</UL>
<P><STRONG><a name="[c6]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[be]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[121]"></a>card_info_get</STRONG> (Thumb, 330 bytes, Stack size 112 bytes, tf_app.o(i.card_info_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 1304 + Unknown Stack Size
<LI>Call Chain = card_info_get &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_information_get
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_capacity_get
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[156]"></a>clust2sect</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[e4]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[11b]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[120]"></a>disk_initialize</STRONG> (Thumb, 142 bytes, Stack size 88 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 1280 + Unknown Stack Size
<LI>Call Chain = disk_initialize &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_information_get
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_mode_config
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[1ad]"></a>disk_ioctl</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
</UL>

<P><STRONG><a name="[141]"></a>disk_read</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[143]"></a>disk_status</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[162]"></a>disk_write</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[fe]"></a>dma_channel_disable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[fc]"></a>dma_channel_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0DmaInit
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[106]"></a>dma_channel_subperipheral_select</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_channel_subperipheral_select))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0DmaInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDmaInit
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[105]"></a>dma_circulation_disable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_circulation_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_circulation_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0DmaInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDmaInit
</UL>

<P><STRONG><a name="[102]"></a>dma_deinit</STRONG> (Thumb, 166 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0DmaInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDmaInit
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[ff]"></a>dma_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[fd]"></a>dma_flag_get</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[167]"></a>dma_flow_controller_config</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_flow_controller_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[f9]"></a>dma_memory_address_config</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_memory_address_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_memory_address_config
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[166]"></a>dma_multi_data_mode_init</STRONG> (Thumb, 352 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_multi_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_multi_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[104]"></a>dma_single_data_mode_init</STRONG> (Thumb, 340 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_single_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_single_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0DmaInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDmaInit
</UL>

<P><STRONG><a name="[103]"></a>dma_single_data_para_struct_init</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_single_data_para_struct_init))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDmaInit
</UL>

<P><STRONG><a name="[fa]"></a>dma_transfer_number_config</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_transfer_number_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_transfer_number_config
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[12c]"></a>dma_transfer_number_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_transfer_number_get))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[d9]"></a>ebtn_combo_btn_add_btn</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, ebtn.o(i.ebtn_combo_btn_add_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ebtn_combo_btn_add_btn &rArr; ebtn_get_btn_index_by_key_id
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_get_btn_index_by_key_id
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_combo_btn_add_btn_by_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnPeriphInit
</UL>

<P><STRONG><a name="[16a]"></a>ebtn_combo_btn_add_btn_by_idx</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ebtn.o(i.ebtn_combo_btn_add_btn_by_idx))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ebtn_combo_btn_add_btn_by_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_combo_btn_add_btn
</UL>

<P><STRONG><a name="[169]"></a>ebtn_get_btn_index_by_key_id</STRONG> (Thumb, 66 bytes, Stack size 12 bytes, ebtn.o(i.ebtn_get_btn_index_by_key_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ebtn_get_btn_index_by_key_id
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_combo_btn_add_btn
</UL>

<P><STRONG><a name="[d8]"></a>ebtn_init</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ebtn_init &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnPeriphInit
</UL>

<P><STRONG><a name="[dc]"></a>ebtn_process</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, ebtn.o(i.ebtn_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn &rArr; prv_get_combo_btn_by_key_id
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_get_current_state
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnTask
</UL>

<P><STRONG><a name="[16d]"></a>ebtn_process_with_curr_state</STRONG> (Thumb, 444 bytes, Stack size 48 bytes, ebtn.o(i.ebtn_process_with_curr_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn &rArr; prv_get_combo_btn_by_key_id
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_or
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_get
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_and
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[db]"></a>ebtn_set_combo_suppress_threshold</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ebtn.o(i.ebtn_set_combo_suppress_threshold))
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnPeriphInit
</UL>

<P><STRONG><a name="[da]"></a>ebtn_set_config</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ebtn.o(i.ebtn_set_config))
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnPeriphInit
</UL>

<P><STRONG><a name="[126]"></a>f_close</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = f_close &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[123]"></a>f_mount</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, ff.o(i.f_mount))
<BR><BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[124]"></a>f_open</STRONG> (Thumb, 372 bytes, Stack size 600 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 1960 + Unknown Stack Size
<LI>Call Chain = f_open &rArr; chk_mounted &rArr; disk_initialize &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[127]"></a>f_read</STRONG> (Thumb, 462 bytes, Stack size 64 bytes, ff.o(i.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[175]"></a>f_sync</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[125]"></a>f_write</STRONG> (Thumb, 526 bytes, Stack size 64 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[14d]"></a>ff_convert</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, unicode.o(i.ff_convert))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[148]"></a>ff_wtoupper</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, unicode.o(i.ff_wtoupper))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>

<P><STRONG><a name="[159]"></a>gen_numname</STRONG> (Thumb, 190 bytes, Stack size 40 bytes, ff.o(i.gen_numname))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = gen_numname &rArr; mem_cpy
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[14a]"></a>get_fat</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[177]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, diskio.o(i.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[de]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashPeriphInit
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>

<P><STRONG><a name="[e5]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[e3]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[ec]"></a>gpio_bit_write</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_write))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedDisp
</UL>

<P><STRONG><a name="[d4]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnGetState
</UL>

<P><STRONG><a name="[d7]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedPeriphInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashPeriphInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnPeriphInit
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[df]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedPeriphInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashPeriphInit
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[ea]"></a>i2c_ack_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_ack_config))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[e7]"></a>i2c_clock_config</STRONG> (Thumb, 216 bytes, Stack size 40 bytes, gd32f4xx_i2c.o(i.i2c_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[e6]"></a>i2c_deinit</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, gd32f4xx_i2c.o(i.i2c_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = i2c_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[fb]"></a>i2c_dma_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_dma_config))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[e9]"></a>i2c_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_enable))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[f8]"></a>i2c_flag_clear</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, gd32f4xx_i2c.o(i.i2c_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = i2c_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[f5]"></a>i2c_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_i2c.o(i.i2c_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = i2c_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[f7]"></a>i2c_master_addressing</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_master_addressing))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[e8]"></a>i2c_mode_addr_config</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, gd32f4xx_i2c.o(i.i2c_mode_addr_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = i2c_mode_addr_config
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[f6]"></a>i2c_start_on_bus</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_start_on_bus))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[100]"></a>i2c_stop_on_bus</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_stop_on_bus))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[a5]"></a>main</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1976 + Unknown Stack Size
<LI>Call Chain = main &rArr; TfCardTest &rArr; f_open &rArr; chk_mounted &rArr; disk_initialize &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TaskExeution
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedPeriphInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashPeriphInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnPeriphInit
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[128]"></a>memory_compare</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, tf_app.o(i.memory_compare))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memory_compare
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[138]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[180]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>

<P><STRONG><a name="[10e]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
</UL>

<P><STRONG><a name="[122]"></a>print_sd_card_status</STRONG> (Thumb, 362 bytes, Stack size 24 bytes, tf_app.o(i.print_sd_card_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 1216 + Unknown Stack Size
<LI>Call Chain = print_sd_card_status &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TfCardTest
</UL>

<P><STRONG><a name="[14b]"></a>put_fat</STRONG> (Thumb, 310 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[113]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
</UL>

<P><STRONG><a name="[17c]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clock_config
</UL>

<P><STRONG><a name="[111]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
</UL>

<P><STRONG><a name="[114]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPreConfig
</UL>

<P><STRONG><a name="[115]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPreConfig
</UL>

<P><STRONG><a name="[d6]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LedPeriphInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashPeriphInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BtnPeriphInit
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0DmaInit
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPreConfig
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledDmaInit
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
</UL>

<P><STRONG><a name="[17e]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_deinit
</UL>

<P><STRONG><a name="[17d]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_deinit
</UL>

<P><STRONG><a name="[116]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPreConfig
</UL>

<P><STRONG><a name="[10b]"></a>rtc_current_time_get</STRONG> (Thumb, 96 bytes, Stack size 12 bytes, gd32f4xx_rtc.o(i.rtc_current_time_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadRtc
</UL>

<P><STRONG><a name="[11a]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetRtc
</UL>

<P><STRONG><a name="[18d]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[18e]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[117]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPreConfig
</UL>

<P><STRONG><a name="[160]"></a>sd_block_read</STRONG> (Thumb, 500 bytes, Stack size 40 bytes, sdio_sdcard.o(i.sd_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = sd_block_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[163]"></a>sd_block_write</STRONG> (Thumb, 760 bytes, Stack size 56 bytes, sdio_sdcard.o(i.sd_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_block_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[15e]"></a>sd_bus_mode_config</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_bus_mode_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[13e]"></a>sd_card_capacity_get</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_card_capacity_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sd_card_capacity_get
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;card_info_get
</UL>

<P><STRONG><a name="[13d]"></a>sd_card_information_get</STRONG> (Thumb, 686 bytes, Stack size 12 bytes, sdio_sdcard.o(i.sd_card_information_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sd_card_information_get
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;card_info_get
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[1a2]"></a>sd_card_init</STRONG> (Thumb, 268 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_card_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_init &rArr; r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_get
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[15c]"></a>sd_card_select_deselect</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_card_select_deselect))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_select_deselect &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[15d]"></a>sd_cardstatus_get</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_cardstatus_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_cardstatus_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_sd_card_status
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[15b]"></a>sd_init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, sdio_sdcard.o(i.sd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_init &rArr; sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[118]"></a>sd_interrupts_process</STRONG> (Thumb, 286 bytes, Stack size 8 bytes, sdio_sdcard.o(i.sd_interrupts_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_get
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_clear
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_disable
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_IRQHandler
</UL>

<P><STRONG><a name="[181]"></a>sd_lock_unlock</STRONG> (Thumb, 462 bytes, Stack size 40 bytes, sdio_sdcard.o(i.sd_lock_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = sd_lock_unlock &rArr; sd_card_state_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_sd_card_status
</UL>

<P><STRONG><a name="[161]"></a>sd_multiblocks_read</STRONG> (Thumb, 632 bytes, Stack size 48 bytes, sdio_sdcard.o(i.sd_multiblocks_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[164]"></a>sd_multiblocks_write</STRONG> (Thumb, 878 bytes, Stack size 56 bytes, sdio_sdcard.o(i.sd_multiblocks_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[1a5]"></a>sd_power_on</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, sdio_sdcard.o(i.sd_power_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_set
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_enable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[15f]"></a>sd_transfer_mode_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sdio_sdcard.o(i.sd_transfer_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[1a7]"></a>sd_transfer_stop</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdio_sdcard.o(i.sd_transfer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[19f]"></a>sdio_bus_mode_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_bus_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[19e]"></a>sdio_clock_config</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[1ab]"></a>sdio_clock_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[185]"></a>sdio_command_index_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_command_index_get))
<BR><BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[194]"></a>sdio_command_response_config</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_command_response_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[196]"></a>sdio_csm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_csm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[18f]"></a>sdio_data_config</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_data_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_data_config
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[198]"></a>sdio_data_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_read))
<BR><BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[190]"></a>sdio_data_transfer_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_transfer_config))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[19b]"></a>sdio_data_write</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_write))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[1a4]"></a>sdio_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[192]"></a>sdio_dma_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[19a]"></a>sdio_dma_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[191]"></a>sdio_dsm_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_disable))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[197]"></a>sdio_dsm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[146]"></a>sdio_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[145]"></a>sdio_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[1a0]"></a>sdio_hardware_clock_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_hardware_clock_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[1a9]"></a>sdio_interrupt_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[199]"></a>sdio_interrupt_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1a8]"></a>sdio_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1a6]"></a>sdio_interrupt_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1a3]"></a>sdio_power_state_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[1aa]"></a>sdio_power_state_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_set))
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[186]"></a>sdio_response_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_response_get))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[195]"></a>sdio_wait_type_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_wait_type_set))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[1ac]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
</UL>

<P><STRONG><a name="[e1]"></a>spi_flash_init</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd25qxx.o(i.spi_flash_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = spi_flash_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashPeriphInit
</UL>

<P><STRONG><a name="[e0]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashPeriphInit
</UL>

<P><STRONG><a name="[17f]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[133]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[12b]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[139]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
</UL>

<P><STRONG><a name="[132]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[136]"></a>usart_dma_receive_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_dma_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[137]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[13a]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0Printf
</UL>

<P><STRONG><a name="[12a]"></a>usart_interrupt_disable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[12f]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[129]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[135]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[134]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[9f]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[70]"></a>_get_lc_ctype</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_ctype_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_ctype_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rt_ctype_table.o(.text)
</UL>
<P><STRONG><a name="[1b3]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[13f]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;card_info_get
</UL>

<P><STRONG><a name="[1b1]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[1b2]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[9a]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1ec]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1ed]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[7c]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[80]"></a>_printf_fp_hex</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf2.o(x$fpl$printf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[72]"></a>BtnGetState</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, gd32f470vet6_bsp.o(i.BtnGetState))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = BtnGetState
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gd32f470vet6_bsp.o(i.BtnPeriphInit)
</UL>
<P><STRONG><a name="[101]"></a>OledDmaInit</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, gd32f470vet6_bsp.o(i.OledDmaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OledDmaInit &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_para_struct_init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_disable
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OledPeriphInit
</UL>

<P><STRONG><a name="[10f]"></a>RtcPreConfig</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, gd32f470vet6_bsp.o(i.RtcPreConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = RtcPreConfig &rArr; rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RtcPeriphInit
</UL>

<P><STRONG><a name="[130]"></a>Usart0DmaInit</STRONG> (Thumb, 96 bytes, Stack size 40 bytes, gd32f470vet6_bsp.o(i.Usart0DmaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Usart0DmaInit &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_disable
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart0PeriphInit
</UL>

<P><STRONG><a name="[1af]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[173]"></a>bit_array_and</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_and))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[16c]"></a>bit_array_assign</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, ebtn.o(i.bit_array_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_get_current_state
</UL>

<P><STRONG><a name="[13b]"></a>bit_array_cmp</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ebtn.o(i.bit_array_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[16f]"></a>bit_array_get</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ebtn.o(i.bit_array_get))
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[172]"></a>bit_array_num_bits_set</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_num_bits_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_num_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[174]"></a>bit_array_or</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_or))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_or
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[16b]"></a>ebtn_get_current_state</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, ebtn.o(i.ebtn_get_current_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ebtn_get_current_state &rArr; bit_array_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[16e]"></a>ebtn_process_btn</STRONG> (Thumb, 62 bytes, Stack size 40 bytes, ebtn.o(i.ebtn_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = ebtn_process_btn &rArr; prv_process_btn &rArr; prv_get_combo_btn_by_key_id
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_get
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[171]"></a>ebtn_process_btn_combo</STRONG> (Thumb, 124 bytes, Stack size 48 bytes, ebtn.o(i.ebtn_process_btn_combo))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = ebtn_process_btn_combo &rArr; prv_process_btn &rArr; prv_get_combo_btn_by_key_id
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_num_bits_set
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[182]"></a>ebtn_timer_sub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ebtn.o(i.ebtn_timer_sub))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
</UL>

<P><STRONG><a name="[183]"></a>prv_get_combo_btn_by_key_id</STRONG> (Thumb, 70 bytes, Stack size 12 bytes, ebtn.o(i.prv_get_combo_btn_by_key_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prv_get_combo_btn_by_key_id
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
</UL>

<P><STRONG><a name="[170]"></a>prv_process_btn</STRONG> (Thumb, 878 bytes, Stack size 40 bytes, ebtn.o(i.prv_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = prv_process_btn &rArr; prv_get_combo_btn_by_key_id
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_get_combo_btn_by_key_id
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_timer_sub
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_get
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[e2]"></a>I2C_Bus_Reset</STRONG> (Thumb, 282 bytes, Stack size 24 bytes, oled.o(i.I2C_Bus_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_mode_addr_config
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_enable
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clock_config
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack_config
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_deinit
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[144]"></a>cmdsent_error_check</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, sdio_sdcard.o(i.cmdsent_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cmdsent_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[165]"></a>dma_receive_config</STRONG> (Thumb, 170 bytes, Stack size 64 bytes, sdio_sdcard.o(i.dma_receive_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[168]"></a>dma_transfer_config</STRONG> (Thumb, 172 bytes, Stack size 64 bytes, sdio_sdcard.o(i.dma_transfer_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[17b]"></a>gpio_config</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, sdio_sdcard.o(i.gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = gpio_config &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[184]"></a>r1_error_check</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, sdio_sdcard.o(i.r1_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[187]"></a>r1_error_type_check</STRONG> (Thumb, 174 bytes, Stack size 0 bytes, sdio_sdcard.o(i.r1_error_type_check))
<BR><BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[188]"></a>r2_error_check</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, sdio_sdcard.o(i.r2_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r2_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[189]"></a>r3_error_check</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, sdio_sdcard.o(i.r3_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r3_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[18a]"></a>r6_error_check</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, sdio_sdcard.o(i.r6_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[18b]"></a>r7_error_check</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, sdio_sdcard.o(i.r7_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r7_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[18c]"></a>rcu_config</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdio_sdcard.o(i.rcu_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_config
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[19d]"></a>sd_bus_width_config</STRONG> (Thumb, 242 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_bus_width_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[19c]"></a>sd_card_state_get</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, sdio_sdcard.o(i.sd_card_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = sd_card_state_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_lock_unlock
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[193]"></a>sd_datablocksize_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sdio_sdcard.o(i.sd_datablocksize_get))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1a1]"></a>sd_scr_get</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, sdio_sdcard.o(i.sd_scr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[140]"></a>check_fs</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = check_fs &rArr; disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[14e]"></a>chk_chr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[142]"></a>chk_mounted</STRONG> (Thumb, 898 bytes, Stack size 80 bytes, ff.o(i.chk_mounted))
<BR><BR>[Stack]<UL><LI>Max Depth = 1360 + Unknown Stack Size
<LI>Call Chain = chk_mounted &rArr; disk_initialize &rArr; Usart0Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[147]"></a>cmp_lfn</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, ff.o(i.cmp_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cmp_lfn
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[149]"></a>create_chain</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[14c]"></a>create_name</STRONG> (Thumb, 604 bytes, Stack size 56 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = create_name &rArr; mem_set
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[150]"></a>dir_find</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[155]"></a>dir_next</STRONG> (Thumb, 280 bytes, Stack size 24 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[157]"></a>dir_register</STRONG> (Thumb, 396 bytes, Stack size 56 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fit_lfn
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[151]"></a>dir_sdi</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[15a]"></a>fit_lfn</STRONG> (Thumb, 122 bytes, Stack size 20 bytes, ff.o(i.fit_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fit_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[176]"></a>follow_path</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[154]"></a>mem_cmp</STRONG> (Thumb, 38 bytes, Stack size 20 bytes, ff.o(i.mem_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[158]"></a>mem_cpy</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, ff.o(i.mem_cpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[14f]"></a>mem_set</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, ff.o(i.mem_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[152]"></a>move_window</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[178]"></a>remove_chain</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[153]"></a>sum_sfn</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, ff.o(i.sum_sfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sum_sfn
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[17a]"></a>sync</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, ff.o(i.sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[179]"></a>validate</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[11c]"></a>_soft_delay_</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, system_gd32f4xx.o(i._soft_delay_))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _soft_delay_
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[1ae]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 258 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_240m_25m_hxtal))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[11d]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = system_clock_config &rArr; system_clock_240m_25m_hxtal
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[6f]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL>
<P><STRONG><a name="[c0]"></a>_fp_digits</STRONG> (Thumb, 432 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
